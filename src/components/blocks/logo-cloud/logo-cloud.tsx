import { useTranslations } from 'next-intl';

export default function LogoCloudSection() {
  const t = useTranslations('HomePage.logocloud');

  return (
    <section id="logo-cloud" className="bg-background px-4 py-16">
      <div className="mx-auto max-w-5xl px-6">
        <h2 className="text-center text-xl font-medium">{t('title')}</h2>

        <div className="mx-auto mt-20 flex max-w-4xl flex-wrap items-center justify-center gap-x-12 gap-y-8 sm:gap-x-16 sm:gap-y-12">
          {/* Shopify Logo */}
          <svg
            className="h-6 w-fit"
            viewBox="0 0 109 124"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M74.5 9.8c-.4-.2-.9-.2-1.4-.1-.5.1-11.2 1.1-11.2 1.1s-7.4-7.1-8.2-7.9c-.8-.8-2.4-.6-3-.4 0 0-1.5.5-4 1.2C44.1 1.5 41.2 0 37.5 0c-11.5 0-17.5 14.4-19.2 21.7-5.2 1.6-8.9 2.7-9.4 2.9-2.9.9-3 1-3.4 3.6C5.1 30.8 0 76.9 0 76.9l67.5 11.7 41.5-9.2s-34-75.4-34.5-69.6zM62.4 13.8c-3.2 1-6.8 2.1-10.4 3.2.9-3.6 2.7-8.5 5.6-11.3 1.2-1.2 2.7-2.2 4.8-2.7v10.8zm-7.1-11.4c-1.1.3-2.1.8-3 1.6-3.8 3.7-5.9 10.1-6.7 15.3-3.1 1-6.1 1.9-8.9 2.7 2.7-10.9 8.2-18.2 18.6-19.6zm-21.8 22.9c3.5-1.1 7.4-2.3 11.6-3.6.1 0 .1-.1.2-.1 1.2-4.4 3.1-8.8 5.9-11.9v-.1c.4-.4.8-.8 1.3-1.1-8.5 2.1-13.2 8.7-19 16.8zm44.4-1.1c0-.4 0-.9-.1-1.3-.2-3.2-1.2-5.4-2.7-6.9-.3-.3-.6-.6-1-.8V4.4c1.7.6 3.1 1.7 4.3 3.1 2.4 2.8 3.8 6.9 4.5 11.7-1.6.5-3.2 1-5 1.5zm5.3-1.6c-.8-5.7-2.4-10.3-5.4-13.7C75.6 5.3 72.9 3.8 69.8 3v-.2c0-.4-.3-.7-.7-.7s-.7.3-.7.7V3c-2.8.8-5.9 2.4-8.4 5.1-3.6 3.9-5.8 9.9-6.8 15.2l-2.8.9c1.2-12.3 7.9-19.1 19.5-21.4.4-.1.7-.5.6-.9-.1-.4-.5-.7-.9-.6C57.8 3.2 50.2 11.5 48.8 25.4c-3.1 1-5.9 1.8-8.4 2.6 2.7-11.3 8.5-18.9 19.4-20.3.4-.1.7-.4.6-.8-.1-.4-.4-.7-.8-.6-12.1 1.6-18.4 10.2-21.3 22.7l-1.5.5c2.4-13.4 9.5-21.4 22.2-22.8.4-.1.7-.4.6-.8-.1-.4-.4-.7-.8-.6-13.8 1.5-21.4 10.4-24 24.7-5.2 1.6-8.9 2.7-9.4 2.9-2.9.9-3 1-3.4 3.6C21.1 38.4 16 84.5 16 84.5l51.5 8.9 25.5-5.7s-20.9-46.3-21.2-42.8z"
              fill="#95BF47"
            />
            <path
              d="M73.1 9.7c-.4-.2-.9-.2-1.4-.1-.5.1-11.2 1.1-11.2 1.1s-7.4-7.1-8.2-7.9c-.4-.4-.9-.6-1.4-.6L67.5 88.6l41.5-9.2S74.5 4 73.1 9.7z"
              fill="#5E8E3E"
            />
            <path
              d="M37.5 29.9l-2.9 10.8s-3.2-1.6-7.1-1.6c-5.7 0-6 3.5-6 4.4 0 4.8 12.7 6.6 12.7 17.8 0 8.8-5.6 14.5-13.1 14.5-9 0-13.6-5.6-13.6-5.6l2-6.5s4.8 4.1 8.8 4.1c2.6 0 3.6-2 3.6-3.5 0-6.1-10.4-6.4-10.4-16.7 0-8.6 6.2-16.9 18.7-16.9 4.8 0 7.3 1.4 7.3 1.4z"
              fill="#FFF"
            />
          </svg>

          {/* Next.js Logo */}
          <svg
            className="h-5 w-fit dark:invert"
            viewBox="0 0 394 80"
            fill="currentColor"
          >
            <path d="M262 0h68.5v12.7h-27.2v66.6h-13.6V12.7H262V0ZM149 0v12.7H94v20.4h44.3v12.6H94v21h55v12.6H80.5V0h68.7zm34.3 0h-17.8l63.8 79.4h17.9l-32-39.7 32-39.7h-17.9l-23 28.6-23-28.6zm18.3 56.7-9-11-27.1 33.7h17.8l18.3-22.7z" />
            <path d="M81 79.3 17 0H0v79.3h13.6V17l50.2 62.3H81Zm252.6-.4c-1 0-1.8-.4-2.5-1s-1.1-1.6-1.1-2.6.3-1.8 1-2.5 1.6-1 2.6-1 1.8.3 2.5 1a3.4 3.4 0 0 1 .6 4.3 3.7 3.7 0 0 1-3 1.8zm23.2-33.5h6v23.3c0 2.1-.4 4-1.3 5.5a9.1 9.1 0 0 1-3.8 3.5c-1.6.8-3.5 1.3-5.7 1.3-2 0-3.7-.4-5.3-1s-2.8-1.8-3.7-3.2c-.9-1.3-1.4-3-1.4-5h6c.1.8.3 1.6.7 2.2s1 1.2 1.6 1.5c.7.4 1.5.5 2.4.5 1 0 1.8-.2 2.4-.6a4 4 0 0 0 1.6-1.8c.3-.8.5-1.8.5-3V45.5zm30.9 9.1a4.4 4.4 0 0 0-2-3.3 7.5 7.5 0 0 0-4.3-1.1c-1.3 0-2.4.2-3.3.5-.9.4-1.6 1-2 1.6a3.5 3.5 0 0 0-.3 4c.3.5.7.9 1.3 1.2l1.8 1 2 .5 3.2.8c1.3.3 2.5.7 3.7 1.2a13 13 0 0 1 3.2 1.8 8.1 8.1 0 0 1 3 6.5c0 2-.5 3.7-1.5 5.1a10 10 0 0 1-4.4 3.5c-1.8.8-4.1 1.2-6.8 1.2-2.6 0-4.9-.4-6.8-1.2-2-.8-3.4-2-4.5-3.5a10 10 0 0 1-1.7-5.6h6a5 5 0 0 0 3.5 4.6c1 .4 2.2.6 3.4.6 1.3 0 2.5-.2 3.5-.6 1-.4 1.8-1 2.4-1.7a4 4 0 0 0 .8-2.4c0-.9-.2-1.6-.7-2.2a11 11 0 0 0-2.1-1.4l-3.2-1-3.8-1c-2.8-.7-5-1.7-6.6-3.2a7.2 7.2 0 0 1-2.4-5.7 8 8 0 0 1 1.7-5 10 10 0 0 1 4.3-3.5c2-.8 4-1.2 6.4-1.2 2.3 0 4.4.4 6.2 1.2 1.8.8 3.2 2 4.2 3.4 1 1.4 1.5 3 1.5 5h-5.8z" />
          </svg>

          {/* React Logo */}
          <svg className="h-5 w-fit" viewBox="0 0 24 24" fill="#61DAFB">
            <circle cx="12" cy="12" r="2" />
            <path d="M12,1C18.5,1 24,6.5 24,12C24,17.5 18.5,23 12,23C5.5,23 0,17.5 0,12C0,6.5 5.5,1 12,1ZM12,3C7.6,3 4,6.6 4,11C4,15.4 7.6,19 12,19C16.4,19 20,15.4 20,11C20,6.6 16.4,3 12,3Z" />
            <ellipse
              cx="12"
              cy="12"
              rx="11"
              ry="4"
              fill="none"
              stroke="#61DAFB"
              strokeWidth="1"
            />
            <ellipse
              cx="12"
              cy="12"
              rx="11"
              ry="4"
              fill="none"
              stroke="#61DAFB"
              strokeWidth="1"
              transform="rotate(60 12 12)"
            />
            <ellipse
              cx="12"
              cy="12"
              rx="11"
              ry="4"
              fill="none"
              stroke="#61DAFB"
              strokeWidth="1"
              transform="rotate(120 12 12)"
            />
          </svg>

          {/* TypeScript Logo */}
          <svg className="h-5 w-fit" viewBox="0 0 24 24" fill="#3178C6">
            <rect width="24" height="24" fill="#3178C6" />
            <path
              d="M1.125 0C.502 0 0 .502 0 1.125v21.75C0 23.498.502 24 1.125 24h21.75c.623 0 1.125-.502 1.125-1.125V1.125C24 .502 23.498 0 22.875 0zm17.363 9.75c.612 0 1.154.037 1.627.111a6.38 6.38 0 0 1 1.306.34v2.458a3.95 3.95 0 0 0-.643-.361 5.093 5.093 0 0 0-.717-.26 5.453 5.453 0 0 0-1.426-.2c-.3 0-.573.028-.819.086a2.1 2.1 0 0 0-.623.242c-.17.104-.3.229-.393.374a.888.888 0 0 0-.14.49c0 .196.053.373.156.529.104.156.252.304.443.444s.423.276.696.41c.273.135.582.274.926.416.47.197.892.407 1.266.628.374.222.695.473.963.753.268.279.472.598.614.957.142.359.214.776.214 1.253 0 .657-.125 1.21-.373 1.656a3.033 3.033 0 0 1-1.012 1.085 4.38 4.38 0 0 1-1.487.596c-.566.12-1.163.18-1.79.18a9.916 9.916 0 0 1-1.84-.164 5.544 5.544 0 0 1-1.512-.493v-2.63a5.033 5.033 0 0 0 3.237 1.2c.333 0 .624-.03.872-.09.249-.06.456-.144.623-.25.166-.108.29-.234.373-.38a1.023 1.023 0 0 0-.074-1.089 2.12 2.12 0 0 0-.537-.5 5.597 5.597 0 0 0-.807-.444 27.72 27.72 0 0 0-1.007-.436c-.918-.383-1.602-.852-2.053-1.405-.45-.553-.676-1.222-.676-2.005 0-.614.123-1.141.369-1.582.246-.441.58-.804 1.004-1.089a4.494 4.494 0 0 1 1.47-.629 7.536 7.536 0 0 1 1.77-.201zm-15.113.188h9.563v2.166H9.506v9.646H6.789v-9.646H3.375z"
              fill="white"
            />
          </svg>

          {/* Tailwind CSS Logo */}
          <svg className="h-4 w-fit" viewBox="0 0 24 24" fill="#06B6D4">
            <path d="M12.001,4.8c-3.2,0-5.2,1.6-6,4.8c1.2-1.6,2.6-2.2,4.2-1.8c0.913,0.228,1.565,0.89,2.288,1.624 C13.666,10.618,15.027,12,18.001,12c3.2,0,5.2-1.6,6-4.8c-1.2,1.6-2.6,2.2-4.2,1.8c-0.913-0.228-1.565-0.89-2.288-1.624 C16.337,6.182,14.976,4.8,12.001,4.8z M6.001,12c-3.2,0-5.2,1.6-6,4.8c1.2-1.6,2.6-2.2,4.2-1.8c0.913,0.228,1.565,0.89,2.288,1.624 c1.177,1.194,2.538,2.576,5.512,2.576c3.2,0,5.2-1.6,6-4.8c-1.2,1.6-2.6,2.2-4.2,1.8c-0.913-0.228-1.565-0.89-2.288-1.624 C10.337,13.382,8.976,12,6.001,12z" />
          </svg>

          {/* Vercel Logo */}
          <svg
            className="h-5 w-fit dark:invert"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M12 2L2 19.777h20L12 2z" />
          </svg>

          {/* GitHub Logo */}
          <svg
            className="h-5 w-fit dark:invert"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
          </svg>

          {/* Node.js Logo */}
          <svg className="h-6 w-fit" viewBox="0 0 24 24" fill="#339933">
            <path d="M11.998,24c-0.321,0-0.641-0.084-0.922-0.247l-2.936-1.737c-0.438-0.245-0.224-0.332-0.08-0.383 c0.585-0.203,0.703-0.25,1.328-0.604c0.065-0.037,0.151-0.023,0.218,0.017l2.256,1.339c0.082,0.045,0.197,0.045,0.272,0l8.795-5.076 c0.082-0.047,0.134-0.141,0.134-0.238V6.921c0-0.099-0.053-0.192-0.137-0.242l-8.791-5.072c-0.081-0.047-0.189-0.047-0.271,0 L3.075,6.68C2.99,6.729,2.936,6.825,2.936,6.921v10.15c0,0.097,0.054,0.189,0.139,0.235l2.409,1.392 c1.307,0.654,2.108-0.116,2.108-0.89V7.787c0-0.142,0.114-0.253,0.256-0.253h1.115c0.139,0,0.255,0.112,0.255,0.253v10.021 c0,1.745-0.95,2.745-2.604,2.745c-0.508,0-0.909,0-2.026-0.551L2.28,18.675c-0.57-0.329-0.922-0.945-0.922-1.604V6.921 c0-0.659,0.353-1.275,0.922-1.603l8.795-5.082c0.557-0.315,1.296-0.315,1.848,0l8.794,5.082c0.570,0.329,0.924,0.944,0.924,1.603 v10.15c0,0.659-0.354,1.273-0.924,1.604l-8.794,5.078C12.643,23.916,12.324,24,11.998,24z M19.099,13.993 c0-1.9-1.284-2.406-3.987-2.763c-2.731-0.361-3.009-0.548-3.009-1.187c0-0.528,0.235-1.233,2.258-1.233 c1.807,0,2.473,0.389,2.747,1.607c0.024,0.115,0.129,0.199,0.247,0.199h1.141c0.071,0,0.138-0.031,0.186-0.081 c0.048-0.054,0.074-0.123,0.067-0.196c-0.177-2.098-1.571-3.076-4.388-3.076c-2.508,0-4.004,1.058-4.004,2.833 c0,1.925,1.488,2.457,3.895,2.695c2.88,0.282,3.103,0.703,3.103,1.269c0,0.983-0.789,1.402-2.642,1.402 c-2.327,0-2.839-0.584-3.011-1.742c-0.02-0.124-0.126-0.215-0.253-0.215h-1.137c-0.141,0-0.254,0.112-0.254,0.253 c0,1.482,0.806,3.248,4.655,3.248C17.501,17.007,19.099,15.91,19.099,13.993z" />
          </svg>
        </div>
      </div>
    </section>
  );
}
