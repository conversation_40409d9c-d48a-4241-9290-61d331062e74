'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import type { AppDetectionResult } from '@/types/app-detection';
import { Loader2, Search } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { toast } from 'sonner';

interface AppDetectorFormProps {
  onResult: (result: AppDetectionResult) => void;
  className?: string;
}

export function AppDetectorForm({ onResult, className }: AppDetectorFormProps) {
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  // Handle input change
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setInput(e.target.value);
    },
    []
  );

  const handleFocus = useCallback(() => {
    setIsFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
  }, []);

  // Memoized class names for performance
  const inputClassName = useMemo(() => {
    return cn(
      // 基础样式
      'w-full h-16 text-lg px-6 pr-16 rounded-2xl border-2',
      'transition-all duration-300 ease-in-out',
      // Shopify 配色聚焦状态
      isFocused &&
        'border-[#008060] shadow-lg shadow-[#008060]/20 scale-[1.02]',
      !isFocused && 'border-border hover:border-[#008060]/50',
      // 加载状态
      isLoading && 'opacity-50 cursor-not-allowed'
    );
  }, [isFocused, isLoading]);

  const buttonClassName = useMemo(() => {
    return cn(
      // 基础样式
      'absolute right-2 top-1/2 -translate-y-1/2',
      'h-12 w-12 rounded-full',
      'transition-all duration-300 ease-in-out',
      // Shopify 配色方案
      input.trim() && !isLoading
        ? 'bg-[#008060] hover:bg-[#004C3F] text-white scale-100'
        : 'bg-muted-foreground/20 scale-90'
    );
  }, [input, isLoading]);

  const iconClassName = useMemo(() => {
    return cn(
      'h-5 w-5 transition-transform duration-300',
      isLoading ? 'animate-spin' : 'group-hover:scale-110'
    );
  }, [isLoading]);

  // Handle form submission
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!input.trim()) {
        toast.error('Please enter a Shopify store URL');
        return;
      }

      setIsLoading(true);

      try {
        const response = await fetch('/api/detect-apps', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ url: input.trim() }),
        });

        const data: AppDetectionResult = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to detect apps');
        }

        onResult(data);

        if (data.success) {
          toast.success(`Found ${data.totalApps} apps on this store!`);
        } else {
          toast.error(data.error || 'Not a Shopify store');
        }
      } catch (error) {
        console.error('Error detecting apps:', error);
        toast.error(
          error instanceof Error ? error.message : 'Failed to detect apps'
        );
      } finally {
        setIsLoading(false);
      }
    },
    [input, onResult]
  );

  return (
    <div className={cn('w-full max-w-4xl', className)}>
      <form onSubmit={handleSubmit} className="w-full">
        <div className="relative group">
          <Input
            value={input}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder="Enter Shopify store URL (e.g., example.myshopify.com)"
            className={inputClassName}
            disabled={isLoading}
          />
          <Button
            type="submit"
            size="icon"
            disabled={isLoading || !input.trim()}
            className={buttonClassName}
          >
            {isLoading ? (
              <Loader2 className={iconClassName} />
            ) : (
              <Search className={iconClassName} />
            )}
          </Button>
        </div>
      </form>

      {/* Helper text and Examples */}
      <div className="mt-6 text-center space-y-3">
        <p className="text-sm text-muted-foreground">
          Enter any Shopify store URL to detect installed apps
        </p>
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">Examples:</p>
          <div className="flex flex-wrap justify-center gap-2">
            {[
              'store.sho.com',
              'shop.stereogum.com',
              'shop.in-n-out.com',
              'shop.singletracks.com',
              'shop.spacex.com',
            ].map((example) => (
              <button
                key={example}
                type="button"
                onClick={() => setInput(`https://${example}`)}
                className="px-3 py-1.5 text-sm bg-muted hover:bg-muted/80 rounded-full transition-colors border"
                disabled={isLoading}
              >
                {example}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default AppDetectorForm;
