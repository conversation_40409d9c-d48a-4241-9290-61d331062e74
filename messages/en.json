{"Metadata": {"name": "Shopify Theme Detector", "title": "Shopify Theme Detector – Instantly Identify Any Shopify Store Theme", "description": "Free Shopify theme detector tool. Enter any Shopify store URL to instantly identify the theme name, type, and get direct links to official themes in the Shopify store."}, "Common": {"login": "Log in to <PERSON> Detector", "logout": "Log out", "signUp": "Sign up for Theme Detection", "language": "Switch language", "mode": {"label": "Toggle mode", "light": "Light", "dark": "Dark", "system": "System"}, "theme": {"label": "Toggle theme", "default": "<PERSON><PERSON><PERSON>", "blue": "Blue", "green": "Green", "amber": "Amber", "neutral": "Neutral"}, "copy": "Copy theme info", "saving": "Saving...", "save": "Save", "loading": "Detecting theme...", "cancel": "Cancel", "logoutFailed": "Failed to log out"}, "PricingPage": {"title": "Shopify Theme Detector Pricing", "description": "Choose the best plan for your Shopify theme detection needs", "subtitle": "Choose the best plan for your Shopify theme detection needs", "monthly": "Monthly", "yearly": "Yearly", "PricingCard": {"freePrice": "$0", "perMonth": "/month", "perYear": "/year", "popular": "Popular", "currentPlan": "Current Plan", "yourCurrentPlan": "Your Current Plan", "getStartedForFree": "Start Detecting Themes Free", "getLifetimeAccess": "Get Lifetime Theme Detection", "getStarted": "Start Theme Detection", "notAvailable": "Not Available", "daysTrial": "{days}-day free theme detection trial"}, "CheckoutButton": {"loading": "Loading...", "checkoutFailed": "Failed to open checkout page"}}, "PricePlans": {"free": {"name": "Free Theme Detection", "description": "Start detecting Shopify themes — unlimited and free, forever.", "features": {"feature-1": "Unlimited theme detections", "feature-2": "Official Shopify theme database access", "feature-3": "Instant theme identification", "feature-4": "Direct theme store links", "feature-5": "Community support"}, "limits": {"limit-1": "Basic theme information only", "limit-2": "No detection history", "limit-3": "Standard detection speed"}}, "hobby": {"name": "<PERSON><PERSON>", "description": "Perfect for individuals who need enhanced theme detection features.", "features": {"feature-1": "Unlimited theme detections", "feature-2": "Advanced theme analysis", "feature-3": "Detection history storage", "feature-4": "Batch URL processing", "feature-5": "Email support (24–48h response)", "feature-6": "Export detection results"}, "limits": {"limit-1": "No priority detection", "limit-2": "Standard processing speed"}}, "professional": {"name": "Professional Detector", "description": "Advanced Shopify theme detection for professionals and agencies.", "features": {"feature-1": "Unlimited priority detections", "feature-2": "Advanced theme analytics", "feature-3": "Bulk theme detection API", "feature-4": "Custom theme database", "feature-5": "Priority support (under 24h)", "feature-6": "White-label detection reports", "feature-7": "Team collaboration features"}, "limits": {"limit-1": "For enterprise needs, contact our team", "limit-2": "Advanced features require setup"}}, "pro": {"name": "Pro Theme Detector", "description": "Ultimate Shopify theme detection for power users and teams.", "features": {"feature-1": "Unlimited instant detections", "feature-2": "Priority processing and support", "feature-3": "Advanced theme analytics", "feature-4": "Custom integrations", "feature-5": "Enterprise-grade security"}, "limits": {"limit-1": "Advanced features require technical setup", "limit-2": "Enterprise support available separately"}}, "lifetime": {"name": "Lifetime Theme Detector", "description": "One-time payment for lifetime access to all Shopify theme detection features.", "features": {"feature-1": "Lifetime theme detection access", "feature-2": "Unlimited detections forever", "feature-3": "Priority support for life", "feature-4": "All future detection updates", "feature-5": "Advanced analytics tools", "feature-6": "API access included", "feature-7": "Enterprise-grade features"}}}, "NotFoundPage": {"title": "404", "message": "Sorry, the page you are looking for does not exist.", "backToHome": "Back to home"}, "ErrorPage": {"title": "Oops! Something went wrong!", "tryAgain": "Try again", "backToHome": "Back to home"}, "AboutPage": {"title": "About Shopify Theme Detector", "description": "Learn about our free Shopify theme detector tool that helps identify any Shopify store's theme instantly and accurately.", "authorName": "Shopify Theme Detector Team", "authorBio": "Shopify Theme Detection Specialists", "introduction": "👋 Welcome to Shopify Theme Detector! We've built the most accurate and comprehensive tool for identifying Shopify themes. Our advanced detection algorithm helps e-commerce professionals, developers, and researchers discover which themes power successful Shopify stores. If you have any questions about theme detection, feel free to contact us.", "talkWithMe": "Contact our team", "followMe": "Follow us for theme updates"}, "ChangelogPage": {"title": "Shopify Theme Detector Updates", "description": "Stay up to date with the latest improvements to our Shopify theme detection tool", "subtitle": "Latest updates to our Shopify theme detection capabilities"}, "ContactPage": {"title": "Contact Shopify Theme Detector Team", "description": "Get help with Shopify theme detection or share feedback about our tool", "subtitle": "Need help with Shopify theme detection? We're here to assist you", "form": {"title": "Contact Our Theme Detection Team", "description": "Have questions about Shopify theme detection or need help identifying a specific theme? Reach out to our experts", "name": "Name", "email": "Email", "message": "Message about theme detection", "submit": "Send Message", "submitting": "Sending...", "success": "Message sent successfully", "fail": "Failed to send message", "nameMinLength": "Name must be at least 3 characters", "nameMaxLength": "Name must not exceed 30 characters", "emailValidation": "Please enter a valid email address", "messageMinLength": "Message must be at least 10 characters", "messageMaxLength": "Message must not exceed 500 characters"}}, "Newsletter": {"title": "Shopify Theme Detection Updates", "subtitle": "Join the theme detection community", "description": "Subscribe for the latest Shopify theme detection updates, new features, and theme trend insights", "form": {"email": "Email", "subscribe": "Subscribe to Updates", "subscribing": "Subscribing...", "success": "Subscribed to theme detection updates", "fail": "Failed to subscribe", "emailValidation": "Please enter a valid email address"}}, "AuthPage": {"login": {"title": "Login to <PERSON> Detector", "welcomeBack": "Welcome back to Shopify Theme Detector", "email": "Email", "password": "Password", "signIn": "Sign In to Detector", "signUpHint": "New to theme detection? Sign up", "forgotPassword": "Forgot Password?", "signInWithGoogle": "Sign In with Google", "signInWithGitHub": "Sign In with GitHub", "showPassword": "Show password", "hidePassword": "Hide password", "or": "Or continue with", "emailRequired": "Please enter your email", "passwordRequired": "Please enter your password"}, "register": {"title": "Join <PERSON>", "createAccount": "Create your theme detection account", "name": "Name", "email": "Email", "password": "Password", "signUp": "Start Detecting Themes", "signInHint": "Already detecting themes? Sign in", "checkEmail": "Please check your email inbox", "showPassword": "Show password", "hidePassword": "Hide password", "nameRequired": "Please enter your name", "emailRequired": "Please enter your email", "passwordRequired": "Please enter your password"}, "forgotPassword": {"title": "Reset Theme Detector Password", "email": "Email", "send": "Send reset link", "backToLogin": "Back to login", "checkEmail": "Please check your email inbox", "emailRequired": "Please enter your email"}, "resetPassword": {"title": "Reset Your Password", "password": "Password", "reset": "Reset password", "backToLogin": "Back to theme detector", "showPassword": "Show password", "hidePassword": "Hide password", "minLength": "Password must be at least 8 characters"}, "error": {"title": "Oops! Something went wrong!", "tryAgain": "Please try again.", "backToLogin": "Back to theme detector", "checkEmail": "Please check your email inbox"}, "common": {"termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "byClickingContinue": "By clicking continue, you agree to our ", "and": " and "}}, "BlogPage": {"title": "Blog", "description": "Latest news and updates from our team", "subtitle": "Latest news and updates from our team", "author": "Author", "categories": "Categories", "tableOfContents": "Table of Contents", "readTime": "{minutes} min read", "all": "All", "noPostsFound": "No posts found", "allPosts": "All Posts", "morePosts": "More Posts"}, "DocsPage": {"toc": "Table of Contents", "search": "Search docs", "lastUpdate": "Last updated on", "searchNoResult": "No results", "previousPage": "Previous", "nextPage": "Next", "chooseLanguage": "Select language", "title": "MkSaaS Docs", "homepage": "Homepage", "blog": "Blog"}, "Marketing": {"navbar": {"features": {"title": "Detection Features"}, "pricing": {"title": "Theme Detector Pricing"}, "blog": {"title": "Blog"}, "tools": {"title": "Tools"}, "docs": {"title": "Detection Guide"}, "pages": {"title": "Resources", "items": {"about": {"title": "About Theme Detector", "description": "Learn about our Shopify theme detection mission"}, "contact": {"title": "Contact Theme Experts", "description": "Get help with Shopify theme detection"}, "changelog": {"title": "Detection Updates", "description": "Latest improvements to theme detection"}, "cookiePolicy": {"title": "<PERSON><PERSON>", "description": "How we use cookies for theme detection"}, "privacyPolicy": {"title": "Privacy Policy", "description": "Your privacy in theme detection matters"}, "termsOfService": {"title": "Terms of Service", "description": "Terms for using our theme detector"}}}, "ai": {"title": "Detection Tools", "items": {"text": {"title": "Theme Analysis", "description": "Advanced Shopify theme analysis tools"}, "image": {"title": "Visual Detection", "description": "Detect themes through visual analysis"}, "video": {"title": "Video Tutorials", "description": "Learn theme detection techniques"}, "audio": {"title": "Audio Guides", "description": "Listen to theme detection tips"}}}}, "footer": {"tagline": "Detect Shopify themes instantly - fast, accurate, and completely free", "product": {"title": "Theme Detector", "items": {"features": "Detection Features", "pricing": "Detector Pricing", "faq": "Theme Detection FAQ"}}, "tools": {"title": "Tools", "items": {"themeDetector": "Shopify Theme Detector", "appDetector": "Shopify App Detector"}}, "resources": {"title": "Detection Resources", "items": {"blog": "Blog", "docs": "Documentation", "changelog": "Changelog"}}, "company": {"title": "Company", "items": {"about": "About Theme Detector", "contact": "Contact Theme Experts"}}, "legal": {"title": "Legal", "items": {"cookiePolicy": "<PERSON><PERSON>", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service"}}}, "avatar": {"dashboard": "Theme Dashboard", "billing": "Detector Billing", "settings": "Detection Settings"}}, "Dashboard": {"dashboard": {"title": "Theme Detection Dashboard"}, "admin": {"title": "Theme Detector Admin", "users": {"title": "Theme Detector Users", "fakeData": "Note: Demo data for theme detection users", "error": "Failed to get theme detector users", "search": "Search theme detector users...", "columns": {"columns": "Columns", "name": "Name", "email": "Email", "role": "Role", "createdAt": "Joined Theme Detector", "customerId": "Customer ID", "status": "Detection Status", "banReason": "Ban Reason", "banExpires": "Ban Expires"}, "noResults": "No theme detector users found", "firstPage": "First Page", "lastPage": "Last Page", "nextPage": "Next Page", "previousPage": "Previous Page", "rowsPerPage": "Users per page", "page": "Page", "loading": "Loading theme detector users...", "admin": "Theme Admin", "user": "Theme User", "email": {"verified": "<PERSON><PERSON>", "unverified": "Email Unverified"}, "emailCopied": "Email copied to clipboard", "banned": "Banned from theme detection", "active": "Active theme detector", "joined": "Joined", "updated": "Updated", "ban": {"reason": "Ban Reason", "reasonPlaceholder": "Reason for banning from theme detection", "defaultReason": "Misuse of theme detector", "never": "Never", "expires": "Ban Expires", "selectDate": "Select Date", "button": "Ban from Theme Detection", "success": "User banned from theme detection", "error": "Failed to ban user"}, "unban": {"button": "Restore Theme Detection Access", "success": "Theme detection access restored", "error": "Failed to restore access"}, "close": "Close"}}, "settings": {"title": "Theme Detector Settings", "profile": {"title": "Theme Detector Profile", "description": "Manage your theme detection account information", "avatar": {"title": "Avatar", "description": "Upload your theme detector profile picture", "recommendation": "A profile picture helps identify you in the theme detection community", "uploading": "Uploading...", "uploadAvatar": "Upload Avatar", "success": "Avatar updated successfully", "fail": "Failed to update avatar"}, "name": {"title": "Display Name", "description": "Your name in the theme detection community", "placeholder": "Enter your name", "minLength": "Please use 3 characters at minimum", "maxLength": "Please use 30 characters at maximum", "hint": "Please use 3-30 characters for your name", "success": "Name updated successfully", "fail": "Failed to update name", "saving": "Saving...", "save": "Save"}}, "billing": {"title": "Theme Detector Billing", "description": "Manage your theme detection subscription and billing", "status": {"active": "Active Theme Detection", "trial": "Theme Detection Trial", "canceled": "Detection Canceled", "free": "Free Theme Detection", "lifetime": "Lifetime Theme Detection"}, "interval": {"month": "month", "year": "year", "oneTime": "one-time"}, "currentPlan": {"title": "Current Detection Plan", "description": "Your current theme detection plan details", "noPlan": "You have no active detection plan"}, "CustomerPortalButton": {"loading": "Loading...", "createCustomerPortalFailed": "Failed to open billing portal"}, "price": "Detection plan price:", "nextBillingDate": "Next detection billing:", "trialEnds": "Detection trial ends:", "serviceEnds": "Detection service ends:", "freePlanMessage": "You're on the free theme detection plan with unlimited basic detections", "lifetimeMessage": "You have lifetime access to all theme detection features", "manageSubscription": "Manage Detection Subscription", "manageBilling": "Manage Detection Billing", "upgradePlan": "Upgrade Detection Plan", "retry": "Retry", "errorMessage": "Failed to get detection plan data"}, "notification": {"title": "Theme Detection Notifications", "description": "Manage your theme detection notification preferences", "newsletter": {"title": "Theme Detection Updates", "description": "Get notified about new theme detection features and insights", "label": "Subscribe to theme detection updates", "hint": "Stay updated with the latest Shopify theme trends and detection improvements", "emailRequired": "Email is required for theme detection updates", "subscribeSuccess": "Successfully subscribed to theme detection updates", "subscribeFail": "Failed to subscribe to theme updates", "unsubscribeSuccess": "Successfully unsubscribed from theme updates", "unsubscribeFail": "Failed to unsubscribe from theme updates", "error": "Error updating theme detection subscription"}}, "security": {"title": "Account Security", "description": "Secure your theme detection account", "updatePassword": {"title": "Change Password", "description": "Update your theme detector account password", "currentPassword": "Current Password", "currentRequired": "Current password is required", "newPassword": "New Password", "newMinLength": "Password must be at least 8 characters", "hint": "Use a strong password to protect your theme detection account", "showPassword": "Show password", "hidePassword": "Hide password", "success": "Password updated successfully", "fail": "Failed to update password", "saving": "Saving...", "save": "Save"}, "resetPassword": {"title": "Reset Password", "description": "Reset your theme detector account password", "info": "Resetting your password will allow you to sign in using email and password in addition to social login methods. You'll receive an email with reset instructions", "button": "Reset Password"}, "deleteAccount": {"title": "Delete Theme Detector Account", "description": "Permanently remove your theme detection account and all data", "warning": "This will delete all your theme detection history and cannot be undone", "button": "Delete Account", "confirmTitle": "Delete Theme Detector Account", "confirmDescription": "Are you sure you want to delete your theme detection account? All detection history will be lost.", "confirm": "Delete", "cancel": "Cancel", "deleting": "Deleting...", "success": "Theme detector account deleted", "fail": "Failed to delete account"}}}, "upgrade": {"title": "Upgrade Theme Detection", "description": "Upgrade to Pro for advanced theme detection features", "button": "Upgrade Detection Plan"}}, "Mail": {"common": {"team": "Shopify Theme Detector Team", "copyright": "©️ {year} Shopify Theme Detector. All Rights Reserved."}, "verifyEmail": {"title": "Welcome to Shopify Theme Detector, {name}!", "body": "Please click the link below to verify your email and start detecting Shopify themes.", "confirmEmail": "Verify Email & Start Detecting", "subject": "Verify your Shopify Theme Detector account"}, "forgotPassword": {"title": "Reset your Theme Detector password, {name}.", "body": "Click the link below to reset your Shopify Theme Detector account password.", "resetPassword": "Reset Theme Detector Password", "subject": "Reset your Theme Detector password"}, "subscribeNewsletter": {"body": "Thank you for subscribing to Shopify Theme Detector updates! We'll keep you informed about new detection features, theme trends, and e-commerce insights.", "subject": "Welcome to Theme Detection Updates"}, "contactMessage": {"name": "Name: {name}", "email": "Email: {email}", "message": "Theme Detection Message: {message}", "subject": "Theme Detection Support Request"}}, "HomePage": {"title": "Shopify Theme Detector – Discover Any Shopify Theme Fast", "description": "Instantly identify any Shopify store's theme with our free Shopify theme detector - fast, accurate, and comprehensive Shopify theme detection tool", "hero": {"title": "Shopify Theme Detector", "description": "Instantly identify any Shopify store's theme with our powerful Shopify theme detector. Enter a URL and discover the theme name, type, and get direct links to official themes in the Shopify store.", "introduction": "The Ultimate Shopify Theme Detector", "primary": "Start Shopify Theme Detection", "secondary": "See Shopify Theme Detector Demo"}, "logocloud": {"title": "Trusted by e-commerce professionals worldwide for Shopify theme detection"}, "useCases": {"title": "Shopify Theme Detection Use Cases", "subtitle": "Who Benefits from Our Shopify Theme Detector", "description": "Whether you're building your first Shopify store or managing multiple e-commerce projects, our Shopify theme detector provides valuable insights for every stage of your journey.", "tryNow": "Start Detecting Shopify Themes", "items": {"item-1": {"title": "Dropshipping & E‑commerce Entrepreneurs", "description": "Using Shopify Theme Detector, easily uncover competitor Shopify themes in seconds. Replicate proven designs, layout flows, and high-converting UI patterns—accelerate your store setup and conversions by modeling what works."}, "item-2": {"title": "Design & Marketing Teams", "description": "Shopify Theme Detector helps teams instantly identify if a website is built on Shopify, what theme it uses, and whether it’s official or custom. Cut minutes off your research workflow and deliver faster, more accurate proposals."}, "item-3": {"title": "Theme & Plugin Developers", "description": "With Shopify Theme Detector, developers can track trending Shopify themes across industries and niches. Use insights to guide feature roadmaps, app compatibility, pricing strategy, and content positioning in the Shopify ecosystem."}, "item-4": {"title": "Market Researchers", "description": "Leverage Shopify Theme Detector to analyze theme adoption trends in e‑commerce verticals. Discover which Shopify themes lead in specific niches and gather competitive intelligence at scale."}, "item-5": {"title": "SEO & Conversion Specialists", "description": "SEO and conversion pros use Shopify Theme Detector to audit top-performing Shopify stores, analyze theme layouts, speed metrics, and UX design. Optimize your own store strategy using real data-driven benchmarks."}, "item-6": {"title": "Brand Builders & Strategy Consultants", "description": "Consultants can reference Shopify Theme Detector insights to understand how leading Shopify brands structure their site layouts, navigation, and branding. Inform client strategy with reliable theme usage data."}}}, "demo": {"title": "See Shopify Theme Detection in Action", "subtitle": "From Shopify Store URL to Theme in Seconds", "description": "Watch how our advanced Shopify theme detector analyzes any Shopify store URL and instantly identifies the theme — no technical skills or manual effort needed with our Shopify theme detector.", "tryItNow": "Try Shopify Theme Detection Now", "features": {"feature-1": {"title": "Simple Shopify URL Input", "description": "Simply enter any Shopify store URL into our theme detector — no technical knowledge or special tools required"}, "feature-2": {"title": "Instant Shopify Theme Detection", "description": "Our advanced system automatically analyzes the Shopify store and identifies the theme in seconds"}, "feature-3": {"title": "Detailed Shopify Theme Results", "description": "Get comprehensive Shopify theme information including name, type, official/custom status, and direct theme store links"}}}, "howItWorks": {"title": "Simple & Accurate Shopify Theme Detection", "subtitle": "How Our Shopify Theme Detector Works", "description": "Follow three simple steps to detect Shopify themes instantly. Our advanced Shopify Theme Detector reveals theme names, customization status, and more—perfect for competitive research, design inspiration, or Shopify audits.", "steps": {"step-1": {"title": "Paste Shopify Store URL", "description": "Enter any Shopify store URL into our Shopify Theme Detector. Whether it uses a custom domain or a myshopify.com subdomain, our tool ensures seamless and accurate Shopify theme detection.", "details": ["Works with both myshopify.com and custom domains", "Instantly validates Shopify store URLs", "No registration needed—completely free to use", "Analyze any Shopify store theme with one click"]}, "step-2": {"title": "Advanced Theme Analysis", "description": "Our Shopify Theme Detector scans the website’s HTML, JavaScript, and CSS to uncover detailed theme data. Whether it’s an official, premium, or customized theme, our detection engine identifies it precisely.", "details": ["Detects standard, premium, and custom Shopify themes", "Recognizes modified and rebranded theme versions", "Compatible with all Shopify storefront layouts", "Code-level theme detection for maximum accuracy"]}, "step-3": {"title": "View Full Theme Report", "description": "Within seconds, our Shopify Theme Detector displays a complete report including theme name, customization level, and a link to the official Shopify Theme Store if available.", "details": ["Displays Shopify theme name and internal theme ID", "Flags whether the theme is original or customized", "Provides direct link to the theme’s official listing", "Ideal for Shopify theme audits and competitor analysis"]}}, "getStarted": "Start Detecting Themes"}, "aiCapabilities": {"title": "Shopify Theme Detection Technology", "subtitle": "Accurate Shopify Theme Detector Based on Source Code Analysis", "description": "Our Shopify Theme Detector works by scanning a store’s HTML source code and extracting structured data from the Shopify.theme object. By analyzing the Shopify.theme schema, our tool provides instant and accurate insights about the theme name, ID, version, and whether it's official or custom-built.", "capability-1": "Real-Time Theme Detection – Instantly extract Shopify theme details by analyzing the Shopify.theme object in a store’s HTML source code.", "capability-2": "No Login or API Required – Our Shopify Theme Detector works directly in the browser without requiring Shopify API credentials or store backend access.", "capability-3": "Official and Custom Theme Support – Detect both Shopify Theme Store templates and custom-developed themes with full metadata visibility.", "capability-4": "Works on All Shopify Domains – Compatible with both custom domains and *.myshopify.com stores using standard Shopify architecture.", "capability-5": "Lightweight and Developer-Friendly – Designed for speed and simplicity, our Shopify Theme Detector is ideal for marketers, designers, and developers researching store setups."}, "comparison": {"title": "Why Choose Us", "subtitle": "Better Than Traditional Methods", "description": "See how our advanced Shopify theme detector outperforms traditional manual methods and other tools in the market.", "traditional": {"title": "Traditional Methods", "subtitle": "Manual inspection and basic tools with limited capabilities", "item-1": "Manual inspection of source code", "item-2": "Limited theme database coverage", "item-3": "Time-consuming analysis process", "item-4": "Requires technical knowledge", "item-5": "Often inaccurate results", "item-6": "No direct theme store links", "item-7": "Paid subscriptions required", "item-8": "Complex setup and configuration"}, "shopifyThemeDetector": {"title": "Our Shopify Theme Detector", "subtitle": "Advanced automated detection with comprehensive features", "item-1": "Instant automated detection", "item-2": "Comprehensive official theme database", "item-3": "Results in seconds, not hours", "item-4": "No technical skills required", "item-5": "High accuracy detection algorithm", "item-6": "Direct links to Shopify theme store", "item-7": "Completely free to use", "item-8": "Simple one-click operation"}}, "integration": {"title": "Integrations", "subtitle": "Integrate with your favorite tools", "description": "Connect seamlessly with popular platforms and services to enhance your workflow.", "learnMore": "Learn More", "items": {"item-1": {"title": "Google Gemini", "description": "Amet praesentium deserunt ex commodi tempore fuga voluptatem."}, "item-2": {"title": "Replit", "description": "Amet praesentium deserunt ex commodi tempore fuga voluptatem."}, "item-3": {"title": "MagicUI", "description": "Amet praesentium deserunt ex commodi tempore fuga voluptatem."}, "item-4": {"title": "VSCodium", "description": "Amet praesentium deserunt ex commodi tempore fuga voluptatem."}, "item-5": {"title": "MediaWiki", "description": "Amet praesentium deserunt ex commodi tempore fuga voluptatem."}, "item-6": {"title": "Google PaLM", "description": "Amet praesentium deserunt ex commodi tempore fuga voluptatem."}}}, "integration2": {"title": "Integrate with your favorite tools", "description": "Connect seamlessly with popular platforms and services to enhance your workflow.", "primaryButton": "Get Started", "secondaryButton": "See <PERSON><PERSON>"}, "features": {"title": "What Makes Shopify Theme Detector Stand Out", "subtitle": "Why Choose Our Shopify Theme Detector", "description": "Explore the powerful capabilities that make Shopify Theme Detector the most trusted and accurate tool for identifying themes used on Shopify stores.", "items": {"item-1": {"title": "Instant Shopify Theme Detection", "description": "Get immediate results by entering any Shopify store URL. Shopify Theme Detector analyzes the store in real-time and reveals the exact theme being used, whether official or custom."}, "item-2": {"title": "Highly Accurate Theme Identification", "description": "Shopify Theme Detector uses Shopify-specific metadata, code structure, and theme signatures to accurately detect even customized or modified themes across any Shopify store."}, "item-3": {"title": "Official Theme Links Provided", "description": "If an official Shopify theme is detected, Shopify Theme Detector gives you a direct link to its Shopify Theme Store page for quick access to demos, details, and pricing."}, "item-4": {"title": "Free to Use, Unlimited Detection", "description": "Shopify Theme Detector is completely free and does not require registration. Detect as many Shopify themes as you like—no limits, no hidden costs."}}}, "features2": {"title": "Features2", "subtitle": "The features of your product", "description": "Write the description of your product here", "feature-1": "Product Feature One", "feature-2": "Product Feature Two", "feature-3": "Product Feature Three", "feature-4": "Product Feature Four"}, "features3": {"title": "Features3", "subtitle": "The features of your product", "description": "Write the description of your product here", "items": {"item-1": {"title": "Product Feature One", "description": "Please write the detailed description of feature one here"}, "item-2": {"title": "Product Feature Two", "description": "Please write the detailed description of feature two here"}, "item-3": {"title": "Product Feature Three", "description": "Please write the detailed description of feature three here"}, "item-4": {"title": "Product Feature Four", "description": "Please write the detailed description of feature four here"}, "item-5": {"title": "Product Feature Five", "description": "Please write the detailed description of feature five here"}, "item-6": {"title": "Product Feature Six", "description": "Please write the detailed description of feature six here"}}}, "pricing": {"title": "Pricing", "subtitle": "Pricing", "description": "Choose the plan that works best for you"}, "faqs": {"title": "Frequently Asked Questions", "subtitle": "Everything you need to know about our Shopify theme detector", "items": {"item-1": {"question": "How does the Shopify theme detector work?", "answer": "Our detector analyzes the HTML structure, JavaScript, and CSS of any Shopify store to identify theme-specific patterns and metadata. It cross-references this information with our comprehensive database of official Shopify themes to provide accurate identification."}, "item-2": {"question": "Is this tool completely free to use?", "answer": "Yes! Our Shopify theme detector is 100% free with no hidden costs, registration requirements, or usage limits. You can detect unlimited themes without any restrictions."}, "item-3": {"question": "How accurate is the Shopify Theme Detector?", "answer": "Our Shopify Theme Detector is designed to deliver reliable and consistent results by analyzing the Shopify.theme object directly from the store’s HTML. While results may vary depending on whether a store uses an official, custom, or modified theme, the detector performs especially well with themes built using standard Shopify architecture. For custom setups, we still provide clear insight into the Shopify structure and theme usage."}, "item-4": {"question": "What types of Shopify stores can I analyze?", "answer": "You can analyze any Shopify store, including those with custom domains (like store.com) and myshopify.com subdomains. Our tool works with stores from all countries and markets."}, "item-5": {"question": "What information do I get about the detected theme?", "answer": "You'll receive the theme name, whether it's an official or custom theme, theme store ID (if applicable), direct links to the Shopify theme store, and pricing information for official themes."}, "item-6": {"question": "Can you detect custom or modified themes?", "answer": "While we excel at identifying official Shopify themes, we can also detect when a store uses a custom theme or heavily modified official theme. In these cases, we'll indicate it as a 'Custom Theme' and provide what information is available."}, "item-7": {"question": "Why does detection sometimes fail?", "answer": "Detection may fail if the website is not a Shopify store, if the store has heavy customizations that obscure theme identifiers, or if the store is password-protected or has restricted access."}, "item-8": {"question": "Do you store or track the URLs I analyze?", "answer": "No, we don't store or track any URLs you analyze. Each detection is processed in real-time and no data is saved on our servers, ensuring your privacy and the privacy of the stores you analyze."}, "item-9": {"question": "Can I use this for competitive research?", "answer": "Absolutely! Many e-commerce professionals use our tool for competitive analysis, market research, and to discover successful theme choices in their industry. It's a valuable tool for understanding market trends."}}}, "testimonials": {"title": "Testimonials", "subtitle": "What our customers are saying", "items": {"item-1": {"name": "<PERSON>", "role": "Software Engineer", "image": "https://randomuser.me/api/portraits/men/1.jpg", "quote": "MkSaaS is really extraordinary and very practical, no need to break your head. A real gold mine."}, "item-2": {"name": "<PERSON>", "role": "GDE - Android", "image": "https://randomuser.me/api/portraits/men/6.jpg", "quote": "With no experience in webdesign I just redesigned my entire website in a few minutes with tailwindcss thanks to MkSaaS."}, "item-3": {"name": "<PERSON><PERSON>", "role": "Tailkits Creator", "image": "https://randomuser.me/api/portraits/men/7.jpg", "quote": "Great work on MkSaaS template. This is one of the best personal website that I have seen so far :)"}, "item-4": {"name": "Anonymous author", "role": "Product Manager", "image": "https://randomuser.me/api/portraits/men/8.jpg", "quote": "I downloaded the one of MkSaaS template which is very clear to understand at the start and you could modify the codes/blocks to fit perfectly on your purpose of the page."}, "item-5": {"name": "<PERSON><PERSON><PERSON>", "role": "Senior Software Engineer", "image": "https://randomuser.me/api/portraits/men/4.jpg", "quote": "MkSaaS is redefining the standard of web design, with these blocks it provides an easy and efficient way for those who love beauty but may lack the time to implement it."}, "item-6": {"name": "<PERSON><PERSON> Fred", "role": "Fullstack Developer", "image": "https://randomuser.me/api/portraits/men/2.jpg", "quote": "I absolutely love MkSaaS! The component blocks are beautifully designed and easy to use, which makes creating a great-looking website a breeze."}, "item-7": {"name": "<PERSON><PERSON><PERSON>", "role": "Founder of ChatExtend", "image": "https://randomuser.me/api/portraits/men/5.jpg", "quote": "MkSaaS is the perfect fusion of simplicity and versatility, enabling us to create UIs that are as stunning as they are user-friendly."}, "item-8": {"name": "<PERSON>", "role": "Fullstack Developer", "image": "https://randomuser.me/api/portraits/men/9.jpg", "quote": "MkSaaS has transformed the way I develop web applications. Their extensive collection of UI components, blocks, and templates has significantly accelerated my workflow."}, "item-9": {"name": "<PERSON><PERSON><PERSON>", "role": "MerakiUI Creator", "image": "https://randomuser.me/api/portraits/men/10.jpg", "quote": "MkSaaS is an elegant, clean, and responsive tailwind css components it's very helpful to start fast with your project."}, "item-10": {"name": "<PERSON>", "role": "TailwindAwesome Creator", "image": "https://randomuser.me/api/portraits/men/11.jpg", "quote": "I love MkSaaS ❤️. The component blocks are well-structured, simple to use, and beautifully designed. It makes it really easy to have a good-looking website in no time."}, "item-11": {"name": "<PERSON>", "role": "@GoogleDevExpert for Android", "image": "https://randomuser.me/api/portraits/men/12.jpg", "quote": "MkSaaS templates are the perfect solution for anyone who wants to create a beautiful and functional website without any web design experience."}, "item-12": {"name": "<PERSON>", "role": "Software Engineer", "image": "https://randomuser.me/api/portraits/men/13.jpg", "quote": "MkSaaS is so well designed that even with a very poor knowledge of web design you can do miracles. Let yourself be seduced!"}}}, "stats": {"title": "Stats", "subtitle": "MkSaaS in numbers", "description": "MkSaaS lets you make AI SaaS in days, simply and effortlessly", "items": {"item-1": {"title": "Stars on GitHub"}, "item-2": {"title": "Active Users"}, "item-3": {"title": "Powered Apps"}}}, "calltoaction": {"title": "Ready to Discover Any Shopify Theme?", "description": "Start detecting Shopify themes instantly with our free Shopify theme detector. No registration required, completely free Shopify theme detector forever.", "primaryButton": "Start Shopify Theme Detection", "secondaryButton": "Browse Official Shopify Themes"}}, "AITextPage": {"title": "AI Text", "description": "MkSaaS lets you make AI SaaS in days, simply and effortlessly", "content": "Working in progress"}, "AIImagePage": {"title": "AI Image", "description": "MkSaaS lets you make AI SaaS in days, simply and effortlessly", "content": "Working in progress"}, "AIVideoPage": {"title": "AI Video", "description": "MkSaaS lets you make AI SaaS in days, simply and effortlessly", "content": "Working in progress"}, "AIAudioPage": {"title": "AI Audio", "description": "MkSaaS lets you make AI SaaS in days, simply and effortlessly", "content": "Working in progress"}, "DetectorPage": {"title": "Shopify Theme Detector", "description": "Detect Shopify themes instantly - analyze any Shopify store URL to identify themes and get detailed theme information", "subtitle": "Free Shopify Theme Detection Tool"}}